# 马上汇接口文档

## 一、 引言

### 1.1 范围

本文档是马上汇对外服务接口规范，主要为接入马上汇的业务需求方提供对外服务支撑数据服务接口描述，包括接口协议，报文格式，安全要求等技术约定，以及相关服务数据产品定义及使用说明。

### 1.2 概念解释

| 概念         |                             解释                             |
| ------------ | :----------------------------------------------------------: |
| appId        | 确定合作后，我们的运营人员会给每个商户分配一个appId作为商户Id，请联系运营人员开通。 |
| secretKey    |           与appId同时提供给客户，作为appId的密码。           |
| access_token | 与appId同时提供给客户 |
| aesKey       | 分配给每个商户的aes算法加解密密钥，用于出入参的加解密。 请注意邮件发送的aes_key是经过base64加密的，请参考Demo代码更快完成与我们系统的对接。 |
| productKey   | 产品编码。 |

### 1.3 协议规则

| 名称     |                             规则                             |
| -------- | :----------------------------------------------------------: |
| 接口域名 | 生产环境：http://10.32.168.11:8009 测试环境：http://10.32.170.3:8009 |
| 传输方式 |             采用Http传输              |
| 提交方式 |                     统一采用POST方式提交                     |
| 数据加密 |                采用AES算法对出入参进行加解密                 |
| 数据格式 |                 提交和返回的数据使用JSON格式                 |
| 字符编码 |                    统一采用UTF-8字符编码                     |

## 二、 接口

### 2.2 申请接口

| 定义     |                描述                |
| -------- | :--------------------------------: |
| 接口功能 | 根据特定入参获取指定产品的业务数据 |
| 接口地址 |   /apply/pca/threeBasicInfoScore   |

#### 2.2.1 请求参数

**Header参数：**

| 字段名称       | 类型   | 必传 |           描述           |
| -------------- | ------ | ---- | :----------------------: |
| x-access-token | String | 是   |         访问令牌         |
| Content-Type   | String | 是   | 约定为：application/json |
| call_no   | String | 是   | 全局调用流水号 |
| project_no   | String | 是   | 项目编号 |
| product_code   | String | 是   | 朴道产品英文名 |
| product_version   | String | 是   | 朴道产品版本号 |
| qry_rsn   | String | 是   | 查询原因 |
| merchant_no   | String | 是   | 商户编号 |

**请求体参数：**

| 字段名称              | 描述       | 类型   | 必传 |                         描述                         |
| --------------------- | ---------- | ------ | ---- | :--------------------------------------------------: |
| appId                 | 商户Id     | String | 是   |                                                      |
| productKey            | 产品编码   | String | 是   |  |
| param                 | 业务参数   | String | 是   |         对业务参数JSON序列化后再进行AES加密          |
| **param业务参数定义** |            |        |      |                                                      |
| outTradeNo            | 外部流水号 | String | 是   |                  长度不超过50个字符                  |
| cryptoType            | 加密方式 | String | 是   |                  MD5 / SHA256                  |
| phone                 | 手机号      | String | 根据需要   |          支持 MD5 / SHA256                                            |
| idCard                 | 身份证号      | String | 根据需要   |       MD5 / SHA256                                               |
| name                 | 姓名      | String | 根据需要   |             MD5 / SHA256                                         |

**param参数样例：**

```
{
    "appId": "your appId",
    "productKey": "your productKey",
    "param": {
        "outTradeNo": "79a69895-31da-4c0e-b079-415d4057f895",
        "cryptoType": "md5"
        "idCard": "06f2d8a7443e7b8bd7e6c52b399fc5c8",
        "phone": "e33c1c370865e35ca1d7d0f06a93e758",
        "name": "294f62389077322e8152d54a6d0c07ed"
    }
}
```

**请求体样例（AES加密后请求参数）：**

```
POST /apply/pca/threeBasicInfoScore HTTP/1.1
Content-Type: application/json
x-access-token: your_access_token
call_no: 全局调用流水号
project_no: 项目编号
product_code: 朴道产品英文名
product_version: 朴道产品版本号
qry_rsn: 查询原因
merchant_no: 商户编号

{
    "appId": "your appId",
    "productKey": "your productKey",
    "param": "ew0KICAgICAgICBpZF9jYXJkOiAiMTk4MDAwMDkwMDIyIiwNCiAgICAgICAgcGhvbmU6ICIxMzg5OTk5MDAwMCIsDQogICAg"
}
```

#### 2.2.2 响应参数

| 字段名称                  | 描述               | 类型    | 必传 |                      备注                       |
| ------------------------- | ------------------ | ------- | ---- | :---------------------------------------------: |
| status                    | 申请状态           | Integer | 是   |             返回码。详情参见附录3.1             |
| message                   | 申请失败描述       | String  | 否   |                    错误信息                     |
| content                   | 业务数据           | String  | 否   | 经过AES加密后的业务数据，需要通过aesKey进行解密 |
| **content解密后业务字段** |                    |         |      |                                                 |
| ${productKey}                 | 键为产品编码的字段 | String  | 是   |                                                 |
| outTradeNo                | 外部流水号         | String  | 是   |               长度不超过50个字符                |
| transactionNo             | 平台流水号         | String  | 是   |                                                 |

**响应样例：**

```
HTTP/1.1 200 OK
Content-Type: application/json;charset=UTF-8
Cache-Control: no-store
Pragma: no-cache

{
    "status": 200,
    "message": "",
    "content":  "JTdCJTBBJTIwJTIwJTIwJTIwJTIwJTIwJTIwJTIwJTIyc2NvcmUlMjIlM0ElMjAlMjA2NjYlMlMjAlMjAlMjAlMjAlN0Q="
}
```

**响应数据经过AES解密后**

```
{
    "status": 200,
    "message": "",
    "content": {
        "${productKey}": 83,
        "outTradeNo": "c337a194-f5b9-43b3-b37e-5dda52db6320",
        "transactionNo": "2022120600000152121"
    }
}
```

## 三、 附录

### 3.1 系统响应编码表

| 响应码 | 错误类型                 |                  描述                  |
| ------ | ------------------------ | :------------------------------------: |
| 200    | 处理成功                 |          系统处理成功返回结果          |
| 202    | 处理中                   | 申请已接收，需调用查询接口查询最终结果 |
| 400    | 非法参数或无效参数       |   缺少必要参数，或者参数值格式不正确   |
| 403    | 无权限或禁止访问         |    错误的身份信息，或者验签名不通过    |
| 404    | 无匹配记录               |               无匹配记录               |
| 408    | 超时                     |                处理超时                |
| 409    | 用户流控超过配置上限     |              请求频率过高              |
| 500    | 处理失败或服务器内部错误 |        处理失败或服务器内部错误        |
| 100001 | 无效用户                 |                                        |
| 100002 | 无效产品                 |                                        |
| 100003 | 产品已下线               |                                        |
| 100004 | 尚未订购相关产品         |                                        |
| 100005 | 外部流水号重复           |                                        |

### 3.2 DEMO 

见附件