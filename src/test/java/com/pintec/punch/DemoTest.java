package com.pintec.punch;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import lombok.Data;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpHost;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Test;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;


public class DemoTest {

    // 测试域名
    private final static String apiUrl = "http://***********:8009";
    // 生产域名
    // private final static String apiUrl = "http://************:8009";

    // AppId和密钥
    private final static String appId = "20241219985809100002";
    private final static String secretKey = "5700c6f830eb457d96c019ee1b4838b9";
    private final static String aesKey = "UYjcu4G3uMYhdEeOvdxEJQ==";

    public static final String PRODUCT_KEY = "M307C0002";


    /**
     * 第一步：获取access_token
     */
    @Test
    public void testForToken() throws Exception {
        String tokenUrl = "/token/getToken";

        // 构建获取token请求体
        TokenParam param = new TokenParam();
        param.setAppId(appId);
        param.setSecretKey(secretKey);
        param.setGrant_type("client_credentials");
        String paramJson = JSON.toJSONString(param);

        StringEntity entity = new StringEntity(paramJson, ContentType.APPLICATION_JSON);
        HttpPost httpPost = new HttpPost(apiUrl + tokenUrl);
        httpPost.setEntity(entity);

        // 发起请求
        CloseableHttpResponse response = httpClient().execute(httpPost);
        String res = EntityUtils.toString(response.getEntity());

        // {"status":200,"message":null,"content":{"access_token":"dd78faa26f8362b92970948f4edbeade"}}
        // 解析请求体 $.content.access_token
        System.out.println("原始响应：" + res);
        System.out.println("当前access_token：" + JSONPath.read(res, "$.content.access_token"));
    }

    private CloseableHttpClient httpClient() throws Exception {
        return HttpClients.custom().setProxy(new HttpHost("127.0.0.1", 8123)).build();
    }

    /**
     * 第二步：申请接口
     */
    @Test
    public void testForApply() throws Exception {
        String scoreUrl = "/apply/pca/threeBasicInfoScore";
        // 使用第一步生成的access_token获取接口得到的access_token
        String access_token = "f19d938d23bc2c02aa5a72be64c2cad0";

        // 此处根据产品需要设置入参
        Map<String, Object> map = new HashMap<>();
        map.put("outTradeNo", UUID.randomUUID());
        map.put("cryptoType", "md5");
        map.put("phone", "ff680812d9cbccf3938646161d160ab8"); // 手机号的MD5值

        // 构建申请请求体
        ScoreParam param = new ScoreParam();
        param.setAppId(appId);
        param.setProductKey(PRODUCT_KEY);
        param.setParam(encrypt(JSON.toJSONString(map)));

        StringEntity entity = new StringEntity(JSON.toJSONString(param), ContentType.APPLICATION_JSON);
        HttpPost httpPost = new HttpPost(apiUrl + scoreUrl);
        httpPost.setEntity(entity);
        httpPost.addHeader("x-access-token", access_token);

        // 发起请求
        CloseableHttpResponse response = httpClient().execute(httpPost);
        String res = EntityUtils.toString(response.getEntity());

        // 解析并解密content内容
        System.out.println(res); // {"status":200,"message":null,"content":"CykiMGSOnJyW1tly0MClHGGO+4/CiGfBO6eQni+gVVeALnfcLo2jKrFwZV8OX2bTcqkw/kKFZsdbBEONrDNOCNWLu/afujRaukgUVavQvTZBf/8Nf26MrBOoWejJ3WCiRLkgK/rSexhlljGXl3gCJU/psq/2x0S4yvsRlcG6xmI3dN3hKYN9rsrv49wg1yVLprHkk2tQyoogDR5ykdcoYNvtibr1BN75vC99yFd2G6aGmc7LmgWFkg4/XMO0M3wAUoBXAQrJGvOKXz9bxB1CopFs4LP5GRbtb7p7m2BKw4Q="}
        String resContent = (String) JSONPath.read(res, "$.content");
        if (resContent != null && !resContent.isEmpty()) {
            String result = decrypt(resContent);
            // 解密后得到的明文content内容即为透传给客户的信息
            System.out.println(result);  // {"M307C0002":"{\"result\":5,\"message\":\"24个月及以上\",\"isTrans\":\"0\",\"orignOpe\":\"1\",\"realOpe\":\"1\"}","outTradeNo":"eda3ff44-253a-41cb-9d00-144be563bd2b","transactionNo":"2024121200000146862"}
        }
    }

    /**
     * 查询接口,一般不用
     */
    @Test
    public void testForQuery() throws Exception {
        String queryUrl = "/query/pca/threeBasicInfoScore";
        // 使用access_token获取接口得到的access_token
        String access_token = "your_access_token";

        // 根据平台流水号或商户流水号进行查询
        Map<String, Object> map = new HashMap<>();
        map.put("transactionNo", "2022120600000025426");
        // map.put("outTradeNo", "b7d1e9f3-0861-49ec-9007-3564dfac72a7");

        // 构建查询请求体
        ScoreParam param = new ScoreParam();
        param.setAppId(appId);
        param.setProductKey(PRODUCT_KEY);
        param.setParam(encrypt(JSON.toJSONString(map)));

        StringEntity entity = new StringEntity(JSON.toJSONString(param), ContentType.APPLICATION_JSON);
        HttpPost httpPost = new HttpPost(apiUrl + queryUrl);
        httpPost.setEntity(entity);
        httpPost.addHeader("x-access-token", access_token);

        // 发起请求
        CloseableHttpResponse response = HttpClients.createDefault().execute(httpPost);
        String res = EntityUtils.toString(response.getEntity());

        // {"status":200,"message":null,"content":"pnDaY0xxfH2+xGP0qhVknJbickX2gacEKhLJdzdjDrJ/+X/587XBk9y/TO2b5mN9mDDAw9lMmjmmlxDepG5tFREXlKsjW70VMJ5rnQrMmeHTuE9wl+LpDaS2pXO7EjY3Gx8YDzvvu/TepNFpCSu01m4TKQcdwU54rO7NQjoRrZ/QanxhZ9ERENxeisfKtcFV"}
        // 解析并解密content内容
        System.out.println(res);
        String resContent = (String) JSONPath.read(res, "$.content");
        if (resContent != null && !resContent.isEmpty()) {
            String result = decrypt(resContent);
            System.out.println(result);
        }
    }

    private String encrypt(String plainText) throws Exception {
        SecretKey key = new SecretKeySpec(Base64.decodeBase64(aesKey), "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, key);
        return Base64.encodeBase64String(cipher.doFinal(plainText.getBytes()));
    }

    private String decrypt(String cipherText) throws Exception {
        SecretKey key = new SecretKeySpec(Base64.decodeBase64(aesKey), "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, key);
        return new String(cipher.doFinal(Base64.decodeBase64(cipherText)));
    }
}

@Data
class ScoreParam {
    /**
     * 商户Id，请与运营人员沟通开通, 必填
     */
    private String appId;
    /**
     * 产品编码，请与运营人员确认, 必填
     */
    private String productKey;
    /**
     * 业务参数，需要进行AES加密, 必填
     */
    private String param;
}

@Data
class TokenParam {
    private String appId;
    private String secretKey;
    private String grant_type;
}